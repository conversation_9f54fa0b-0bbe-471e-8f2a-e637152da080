from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_openai import AzureChatOpenAI
import os
from dotenv import load_dotenv
load_dotenv()

llm  = AzureChatOpenAI(
    model="gpt-4.1",
    api_key = os.getenv("AZURE_OPENAI_KEY"),
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT"),
    api_version = os.getenv("AZURE_OPENAI_VERSION"),
    temperature=0.0001,
)

client = MultiServerMCPClient(
    {
        "math": {
            "command": "python",
            # Replace with absolute path to your math_server.py file
            "args": ["math_server.py"],
            "transport": "stdio",
        },
    }
)

tools = client.get_tools()
agent = create_react_agent(
    model = llm,
    tools = tools,
)
math_response = await agent.ainvoke(
    {"messages": [{"role": "user", "content": "what's (3 + 5) x 12?"}]}
)
# weather_response = await agent.ainvoke(
#     {"messages": [{"role": "user", "content": "what is the weather in nyc?"}]}
# )

